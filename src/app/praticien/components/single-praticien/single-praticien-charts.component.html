<section class="mt-8">
  <div class="px-4 sm:px-6 lg:max-w-6xl lg:mx-auto lg:px-8">
    <ol class="flex items-center w-full text-sm text-gray-500 font-medium sm:text-base">
      <li class="flex md:w-full items-center text-indigo-600 sm:after:content-[''] after:w-full after:h-1 after:border-b after:border-gray-200 after:border-1 after:hidden sm:after:inline-block after:mx-4 xl:after:mx-8">
        <div class="flex items-center whitespace-nowrap after:content-['/'] sm:after:hidden after:mx-2 cursor-pointer" (click)="setActiveType('CCAM')">
          <span [class.bg-indigo-600]="activeType === 'CCAM'" [class.text-white]="activeType === 'CCAM'" [class.bg-gray-100]="activeType !== 'CCAM'" [class.text-gray-600]="activeType !== 'CCAM'" class="w-6 h-6 border border-indigo-200 rounded-full flex justify-center items-center mr-3 text-sm text-white lg:w-10 lg:h-10">1</span>
          CCAM
        </div>
      </li>
      <li class="flex md:w-full items-center text-indigo-600 sm:after:content-[''] after:w-full after:h-1 after:border-b after:border-gray-200 after:border-1 after:hidden sm:after:inline-block after:mx-4 xl:after:mx-8">
        <div class="flex items-center whitespace-nowrap after:content-['/'] sm:after:hidden after:mx-2 cursor-pointer" (click)="setActiveType('NGAP')">
          <span [class.bg-indigo-600]="activeType === 'NGAP'" [class.text-white]="activeType === 'NGAP'" [class.bg-gray-100]="activeType !== 'NGAP'" [class.text-gray-600]="activeType !== 'NGAP'" class="w-6 h-6 border border-gray-200 rounded-full flex justify-center items-center mr-3 lg:w-10 lg:h-10">2</span>
          NGAP
        </div>
      </li>
      <li class="flex md:w-full items-center text-indigo-600">
        <div class="flex items-center cursor-pointer" (click)="setActiveType('LABO')">
          <span [class.bg-indigo-600]="activeType === 'LABO'" [class.text-white]="activeType === 'LABO'" [class.bg-gray-100]="activeType !== 'LABO'" [class.text-gray-600]="activeType !== 'LABO'" class="w-6 h-6 border border-gray-200 rounded-full flex justify-center items-center mr-3 lg:w-10 lg:h-10">3</span>
          LABO
        </div>
      </li>
    </ol>
  </div>

  <div class="px-4 sm:px-6 lg:max-w-6xl lg:mx-auto lg:px-8 mt-4" *ngIf="!error">
    <div *ngIf="loading" class="text-center text-gray-500 py-8">Chargement des graphiques...</div>

    <div *ngIf="!loading" class="grid grid-cols-12 gap-4 items-stretch">
      <!-- Monthly Area Chart -->
      <div class="col-span-12 lg:col-span-8 bg-white p-4 shadow rounded-lg h-full flex flex-col">
        <h5 class="text-lg font-bold text-cyan-700 mb-3">Évolution mensuelle (surface)</h5>
        <div class="flex-1">
          <p-chart type="line" [data]="monthlyChartData" [options]="chartOptions" [style]="{ width: '100%', height: '100%' }"></p-chart>
        </div>
      </div>
      <div class="col-span-12 lg:col-span-4 bg-white p-4 shadow rounded-lg">
        <h5 class="text-lg font-bold text-gray-700 mb-3">Données mensuelles</h5>
        <ul>
          <li *ngFor="let label of monthlyChartData?.labels; let i = index" class="flex justify-between py-2 border-b">
            <span>{{ label }}</span>
            <span>
              <span class="text-blue-600 font-semibold mr-1">{{ monthlyChartData?.datasets[0]?.data[i] || 0 }}</span>
              <span class="text-amber-600 font-semibold mr-1">{{ monthlyChartData?.datasets[1]?.data[i] || 0 }}</span>
              <span class="text-violet-600 font-semibold">{{ monthlyChartData?.datasets[2]?.data[i] || 0 }}</span>
            </span>
          </li>
        </ul>
      </div>

      <!-- Weekly Bar Chart -->
      <div class="col-span-12 lg:col-span-8 bg-white p-4 shadow rounded-lg h-full flex flex-col">
        <h5 class="text-lg font-bold text-gray-700 mb-3">Répartition hebdomadaire (jours)</h5>
        <div class="flex-1">
          <p-chart type="bar" [data]="weeklyChartData" [options]="chartOptions" [style]="{ width: '100%', height: '100%' }"></p-chart>
        </div>
      </div>
      <div class="col-span-12 lg:col-span-4 bg-white p-4 shadow rounded-lg">
        <h5 class="text-lg font-bold text-gray-700 mb-3">Données hebdomadaires</h5>
        <ul>
          <li *ngFor="let label of weeklyChartData?.labels; let i = index" class="flex justify-between py-2 border-b">
            <span>{{ label }}</span>
            <span>
              <span class="text-blue-600 font-semibold mr-1">{{ weeklyChartData?.datasets[0]?.data[i] || 0 }}</span>
              <span class="text-amber-600 font-semibold mr-1">{{ weeklyChartData?.datasets[1]?.data[i] || 0 }}</span>
              <span class="text-violet-600 font-semibold">{{ weeklyChartData?.datasets[2]?.data[i] || 0 }}</span>
            </span>
          </li>
        </ul>
      </div>
    </div>
  </div>

  <div class="px-4 sm:px-6 lg:max-w-6xl lg:mx-auto lg:px-8 mt-4" *ngIf="error">
    <p class="text-red-600">{{ error }}</p>
  </div>
</section>
