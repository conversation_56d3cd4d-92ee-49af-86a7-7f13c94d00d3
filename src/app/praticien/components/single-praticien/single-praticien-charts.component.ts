import { Component, Input, OnChanges } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { environment } from '../../../../environments/environment';
import { DatePeriod } from '../../../core/services/global-filter/global-filter.service';

interface ActiviteApiResponse<T> {
  '@context': string;
  '@id': string;
  '@type': string;
  totalItems: number;
  member: T[];
}

interface ActiviteItem {
  ['@id']: string;
  ['@type']: string;
  code: string;
  description: string;
  dateRealisation: string; // ISO date
  typeActe: string; // CCAM | NGAP | LABO | NABM
  nombreDeRealisation: number;
  mois?: number;
  annee?: number;
  semaineIso?: number;
}

@Component({
  selector: 'app-single-praticien-charts',
  templateUrl: './single-praticien-charts.component.html',
  styleUrls: ['./single-praticien-charts.component.scss']
})
export class SinglePraticienChartsComponent implements OnChanges {
  @Input() hrUser: string | null = null;
  @Input() p1: DatePeriod | null = null;
  @Input() p2: DatePeriod | null = null;
  @Input() p3: DatePeriod | null = null;

  // UI state
  activeType: 'CCAM' | 'NGAP' | 'LABO' = 'CCAM';

  // Chart data
  monthlyChartData: any;
  weeklyChartData: any;
  chartOptions: any;

  private readonly MONTH_LABELS_FR = ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Juin', 'Juil', 'Août', 'Sep', 'Oct', 'Nov', 'Déc'];
  private readonly WEEKDAY_LABELS_FR = ['Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam', 'Dim'];

  loading = false;
  error: string | null = null;

  constructor(private http: HttpClient) {
    this.chartOptions = {
      responsive: true,
      maintainAspectRatio: true,
      aspectRatio: 2,
      plugins: {
        legend: {
          position: 'top'
        },
        tooltip: {
          mode: 'index',
          intersect: false
        }
      },
      elements: {
        line: {
          tension: 0.3,
          fill: true // surface
        },
        point: {
          radius: 2
        }
      },
      scales: {
        x: {
          grid: { display: false }
        },
        y: {
          grid: { color: 'rgba(0,0,0,0.05)' }
        }
      }
    };
  }

  ngOnChanges(): void {
    // Reload charts whenever inputs change and are valid
    if (this.hrUser && (this.p1 || this.p2 || this.p3)) {
      this.loadChartsForType(this.activeType);
    }
  }

  setActiveType(type: 'CCAM' | 'NGAP' | 'LABO'): void {
    if (this.activeType !== type) {
      this.activeType = type;
      if (this.hrUser) {
        this.loadChartsForType(type);
      }
    }
  }

  private loadChartsForType(type: 'CCAM' | 'NGAP' | 'LABO'): void {
    if (!this.hrUser) return;

    this.loading = true;
    this.error = null;

    const params = this.buildParams(type);

    this.http.get<ActiviteApiResponse<ActiviteItem>>(`${environment.api_url}/api/actes`, { params })
      .subscribe({
        next: (resp) => {
          const items = resp.member || [];
          const grouped = this.groupByPeriods(items);
          this.monthlyChartData = this.buildMonthlyAreaChart(grouped);
          this.weeklyChartData = this.buildWeeklyBarChart(grouped);
          this.loading = false;
        },
        error: (err) => {
          console.error('Error loading charts data:', err);
          this.error = 'Erreur lors du chargement des graphiques';
          this.loading = false;
        }
      });
  }

  private buildParams(type: 'CCAM' | 'NGAP' | 'LABO'): HttpParams {
    let params = new HttpParams();
    params = params.set('agent.hrUser', this.hrUser!);

    // LABO includes NABM alias if used in backend
    if (type === 'LABO') {
      params = params.set('typeActe[]', 'LABO');
      params = params.set('typeActe[]', 'NABM');
    } else {
      params = params.set('typeActe', type);
    }

    // Periods
    const setPeriod = (prefix: string, p: DatePeriod | null) => {
      if (p?.dateDebut && p?.dateFin) {
        params = params.set(`${prefix}Start`, this.formatDate(p.dateDebut));
        params = params.set(`${prefix}End`, this.formatDate(p.dateFin));
      }
    };

    setPeriod('p1', this.p1);
    setPeriod('p2', this.p2);
    setPeriod('p3', this.p3);

    // Get many items to aggregate client-side
    params = params.set('itemsPerPage', '10000');

    return params;
  }

  private groupByPeriods(items: ActiviteItem[]): {
    p1: ActiviteItem[]; p2: ActiviteItem[]; p3: ActiviteItem[];
  } {
    const p1: ActiviteItem[] = [];
    const p2: ActiviteItem[] = [];
    const p3: ActiviteItem[] = [];

    items.forEach(it => {
      const d = new Date(it.dateRealisation);
      if (this.isInPeriod(d, this.p1)) p1.push(it);
      if (this.isInPeriod(d, this.p2)) p2.push(it);
      if (this.isInPeriod(d, this.p3)) p3.push(it);
    });

    return { p1, p2, p3 };
  }

  private buildMonthlyAreaChart(grouped: { p1: ActiviteItem[]; p2: ActiviteItem[]; p3: ActiviteItem[]; }) {
    const months = Array.from({ length: 12 }, (_, i) => i + 1);
    const sumByMonth = (arr: ActiviteItem[]) => months.map(m =>
      arr.filter(a => this.getMonth(a) === m)
         .reduce((s, a) => s + (a.nombreDeRealisation || 0), 0)
    );

    const datasetP1 = sumByMonth(grouped.p1);
    const datasetP2 = sumByMonth(grouped.p2);
    const datasetP3 = sumByMonth(grouped.p3);

    return {
      labels: this.MONTH_LABELS_FR,
      datasets: [
        { label: 'P1', data: datasetP1, borderColor: '#3b82f6', backgroundColor: 'rgba(59,130,246,0.25)', fill: true },
        { label: 'P2', data: datasetP2, borderColor: '#f59e0b', backgroundColor: 'rgba(245,158,11,0.25)', fill: true },
        { label: 'P3', data: datasetP3, borderColor: '#8b5cf6', backgroundColor: 'rgba(139,92,246,0.25)', fill: true }
      ]
    };
  }

  private buildWeeklyBarChart(grouped: { p1: ActiviteItem[]; p2: ActiviteItem[]; p3: ActiviteItem[]; }) {
    const idx = [1, 2, 3, 4, 5, 6, 0]; // reorder to start Monday

    const sumByWeekday = (arr: ActiviteItem[]) => idx.map(i =>
      arr.filter(a => this.getWeekday(a) === i)
         .reduce((s, a) => s + (a.nombreDeRealisation || 0), 0)
    );

    const datasetP1 = sumByWeekday(grouped.p1);
    const datasetP2 = sumByWeekday(grouped.p2);
    const datasetP3 = sumByWeekday(grouped.p3);

    const labels = idx.map(i => this.WEEKDAY_LABELS_FR[i === 0 ? 6 : i - 1]);

    return {
      labels,
      datasets: [
        { label: 'P1', data: datasetP1, backgroundColor: 'rgba(59,130,246,0.6)' },
        { label: 'P2', data: datasetP2, backgroundColor: 'rgba(245,158,11,0.6)' },
        { label: 'P3', data: datasetP3, backgroundColor: 'rgba(139,92,246,0.6)' }
      ]
    };
  }

  private getMonth(a: ActiviteItem): number {
    if (a.mois) return a.mois; // API may provide
    try {
      const d = new Date(a.dateRealisation);
      return d.getMonth() + 1;
    } catch {
      return 0;
    }
  }

  private getWeekday(a: ActiviteItem): number {
    try {
      const d = new Date(a.dateRealisation);
      return d.getDay(); // 0=Sun..6=Sat
    } catch {
      return -1;
    }
  }

  private isInPeriod(d: Date, p: DatePeriod | null): boolean {
    if (!p?.dateDebut || !p?.dateFin) return false;
    return d >= p.dateDebut && d <= p.dateFin;
  }

  private formatDate(date: Date): string {
    return date.toISOString().split('T')[0];
  }
}
