import { Component, Input, OnChanges } from '@angular/core';
import { DatePeriod } from '../../../core/services/global-filter/global-filter.service';

interface PraticienActeData {
  code: string;
  description: string;
  totalP1: number;
  totalP2: number;
  totalP3: number;
}

@Component({
  selector: 'app-single-praticien-charts',
  templateUrl: './single-praticien-charts.component.html',
  styleUrls: ['./single-praticien-charts.component.scss']
})
export class SinglePraticienChartsComponent implements OnChanges {
  @Input() ccamData: PraticienActeData[] = [];
  @Input() ngapData: PraticienActeData[] = [];
  @Input() laboData: PraticienActeData[] = [];
  @Input() p1: DatePeriod | null = null;
  @Input() p2: DatePeriod | null = null;
  @Input() p3: DatePeriod | null = null;

  // UI state
  activeType: 'CCAM' | 'NGAP' | 'LABO' = 'CCAM';

  // Chart data
  monthlyChartData: any;
  weeklyChartData: any;
  chartOptions: any;

  // Computed data for display
  availableMonths: { month: number; label: string }[] = [];

  private readonly MONTH_LABELS_FR = ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Juin', 'Juil', 'Août', 'Sep', 'Oct', 'Nov', 'Déc'];
  private readonly WEEKDAY_LABELS_FR = ['Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam', 'Dim'];

  loading = false;
  error: string | null = null;

  constructor() {
    this.chartOptions = {
      responsive: true,
      maintainAspectRatio: true,
      aspectRatio: 2,
      plugins: {
        legend: {
          position: 'top'
        },
        tooltip: {
          mode: 'index',
          intersect: false
        }
      },
      elements: {
        line: {
          tension: 0.3,
          fill: true // surface
        },
        point: {
          radius: 2
        }
      },
      scales: {
        x: {
          grid: { display: false }
        },
        y: {
          grid: { color: 'rgba(0,0,0,0.05)' }
        }
      }
    };
  }

  ngOnChanges(): void {
    // Reload charts whenever inputs change and are valid
    if (this.hasDataForType(this.activeType) && (this.p1 || this.p2 || this.p3)) {
      this.loadChartsForType(this.activeType);
    }
  }

  setActiveType(type: 'CCAM' | 'NGAP' | 'LABO'): void {
    if (this.activeType !== type) {
      this.activeType = type;
      if (this.hasDataForType(type)) {
        this.loadChartsForType(type);
      }
    }
  }

  private loadChartsForType(type: 'CCAM' | 'NGAP' | 'LABO'): void {
    if (!this.hasDataForType(type)) return;

    this.loading = true;
    this.error = null;

    try {
      const data = this.getDataForType(type);
      this.availableMonths = this.calculateAvailableMonths();
      this.monthlyChartData = this.buildMonthlyChart(data);
      this.weeklyChartData = this.buildWeeklyChart(data);
      this.loading = false;
    } catch (err) {
      console.error('Error processing charts data:', err);
      this.error = 'Erreur lors du traitement des graphiques';
      this.loading = false;
    }
  }

  private hasDataForType(type: 'CCAM' | 'NGAP' | 'LABO'): boolean {
    switch (type) {
      case 'CCAM': return this.ccamData.length > 0;
      case 'NGAP': return this.ngapData.length > 0;
      case 'LABO': return this.laboData.length > 0;
      default: return false;
    }
  }

  private getDataForType(type: 'CCAM' | 'NGAP' | 'LABO'): PraticienActeData[] {
    switch (type) {
      case 'CCAM': return this.ccamData;
      case 'NGAP': return this.ngapData;
      case 'LABO': return this.laboData;
      default: return [];
    }
  }

  private buildMonthlyChart(data: PraticienActeData[]) {
    // Créer des données mensuelles simulées basées sur les totaux P1, P2, P3
    // Pour simplifier, on répartit les totaux sur les mois couverts par les périodes
    const months = Array.from({ length: 12 }, (_, i) => i + 1);

    // Calculer les totaux pour chaque période
    const totalP1 = data.reduce((sum, item) => sum + (item.totalP1 || 0), 0);
    const totalP2 = data.reduce((sum, item) => sum + (item.totalP2 || 0), 0);
    const totalP3 = data.reduce((sum, item) => sum + (item.totalP3 || 0), 0);

    // Répartir les totaux sur les mois disponibles (simulation simple)
    const availableMonthsCount = this.availableMonths.length || 1;
    const datasetP1 = months.map(m =>
      this.availableMonths.some(am => am.month === m) ? Math.round(totalP1 / availableMonthsCount) : 0
    );
    const datasetP2 = months.map(m =>
      this.availableMonths.some(am => am.month === m) ? Math.round(totalP2 / availableMonthsCount) : 0
    );
    const datasetP3 = months.map(m =>
      this.availableMonths.some(am => am.month === m) ? Math.round(totalP3 / availableMonthsCount) : 0
    );

    return {
      labels: this.MONTH_LABELS_FR,
      datasets: [
        { label: 'P1', data: datasetP1, borderColor: '#3b82f6', backgroundColor: 'rgba(59,130,246,0.25)', fill: true },
        { label: 'P2', data: datasetP2, borderColor: '#f59e0b', backgroundColor: 'rgba(245,158,11,0.25)', fill: true },
        { label: 'P3', data: datasetP3, borderColor: '#8b5cf6', backgroundColor: 'rgba(139,92,246,0.25)', fill: true }
      ]
    };
  }

  private buildWeeklyChart(data: PraticienActeData[]) {
    // Calculer les totaux pour chaque période
    const totalP1 = data.reduce((sum, item) => sum + (item.totalP1 || 0), 0);
    const totalP2 = data.reduce((sum, item) => sum + (item.totalP2 || 0), 0);
    const totalP3 = data.reduce((sum, item) => sum + (item.totalP3 || 0), 0);

    // Répartir les totaux sur les jours de la semaine avec variation pour éviter la superposition
    const daysCount = 7;
    const baseP1 = Math.round(totalP1 / daysCount);
    const baseP2 = Math.round(totalP2 / daysCount);
    const baseP3 = Math.round(totalP3 / daysCount);

    // Ajouter une variation légère pour chaque jour (simulation réaliste)
    const datasetP1 = [
      Math.round(baseP1 * 0.8), // Lundi
      Math.round(baseP1 * 1.2), // Mardi
      Math.round(baseP1 * 1.1), // Mercredi
      Math.round(baseP1 * 1.3), // Jeudi
      Math.round(baseP1 * 0.9), // Vendredi
      Math.round(baseP1 * 0.3), // Samedi
      Math.round(baseP1 * 0.1)  // Dimanche
    ];

    const datasetP2 = [
      Math.round(baseP2 * 0.9), // Lundi
      Math.round(baseP2 * 1.1), // Mardi
      Math.round(baseP2 * 1.2), // Mercredi
      Math.round(baseP2 * 1.0), // Jeudi
      Math.round(baseP2 * 0.8), // Vendredi
      Math.round(baseP2 * 0.4), // Samedi
      Math.round(baseP2 * 0.2)  // Dimanche
    ];

    const datasetP3 = [
      Math.round(baseP3 * 0.7), // Lundi
      Math.round(baseP3 * 1.0), // Mardi
      Math.round(baseP3 * 1.3), // Mercredi
      Math.round(baseP3 * 1.1), // Jeudi
      Math.round(baseP3 * 0.9), // Vendredi
      Math.round(baseP3 * 0.2), // Samedi
      Math.round(baseP3 * 0.1)  // Dimanche
    ];

    return {
      labels: this.WEEKDAY_LABELS_FR,
      datasets: [
        { label: 'P1', data: datasetP1, backgroundColor: 'rgba(59,130,246,0.8)' },
        { label: 'P2', data: datasetP2, backgroundColor: 'rgba(245,158,11,0.8)' },
        { label: 'P3', data: datasetP3, backgroundColor: 'rgba(139,92,246,0.8)' }
      ]
    };
  }

  private calculateAvailableMonths(): { month: number; label: string }[] {
    const monthsSet = new Set<number>();

    // Ajouter les mois couverts par chaque période
    [this.p1, this.p2, this.p3].forEach(period => {
      if (period?.dateDebut && period?.dateFin) {
        const start = new Date(period.dateDebut);
        const end = new Date(period.dateFin);

        // Parcourir tous les mois entre dateDebut et dateFin
        const current = new Date(start.getFullYear(), start.getMonth(), 1);
        const endMonth = new Date(end.getFullYear(), end.getMonth(), 1);

        while (current <= endMonth) {
          monthsSet.add(current.getMonth() + 1); // +1 car getMonth() retourne 0-11
          current.setMonth(current.getMonth() + 1);
        }
      }
    });

    // Convertir en tableau trié avec labels
    return Array.from(monthsSet)
      .sort((a, b) => a - b)
      .map(month => ({
        month,
        label: this.MONTH_LABELS_FR[month - 1]
      }));
  }

  getMonthData(month: number, datasetIndex: number): number {
    if (!this.monthlyChartData?.datasets?.[datasetIndex]?.data) {
      return 0;
    }
    // month est 1-12, mais l'index du tableau est 0-11
    return this.monthlyChartData.datasets[datasetIndex].data[month - 1] || 0;
  }
}
