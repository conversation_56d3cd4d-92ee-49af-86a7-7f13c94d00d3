<!-- Breadcrumb -->
<app-breadcrumb [items]="breadcrumbItems"></app-breadcrumb>

<!-- Loading state -->
<div *ngIf="loading" class="flex justify-center items-center h-64">
  <p-progressSpinner strokeWidth="3" animationDuration="1s"></p-progressSpinner>
</div>

<!-- Error state -->
<div *ngIf="error && !loading" class="p-4">
  <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
    <strong>Erreur:</strong> {{ error }}
  </div>
</div>

<!-- Content when data is loaded -->
<div *ngIf="!loading && !error && agent" class="min-h-screen bg-gray-50">

  <!-- Subheader avec profil du praticien -->
  <div class="bg-white shadow">
    <div class="px-4 py-6 sm:px-6 lg:px-8">
      <div class="md:flex md:items-center md:justify-between">
        <div class="flex-1 min-w-0">
          <!-- Profile -->
          <div class="flex items-center">
            <!-- Avatar -->
            <div class="flex-shrink-0">
              <div class="h-16 w-16 rounded-full bg-gradient-to-r from-cyan-500 to-blue-600 flex items-center justify-center text-white text-xl font-bold">
                {{ getInitials(agent) }}
              </div>
            </div>

            <!-- Informations principales -->
            <div class="ml-4">
              <div class="flex items-center">
                <h1 class="text-2xl font-bold leading-7 text-gray-900 sm:leading-9 sm:truncate">
                  {{ getFullNameWithTitle(agent) }}
                </h1>
                <span
                  class="ml-3 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                  [ngClass]="{
                    'bg-green-100 text-green-800': isPresentAtCHU(agent),
                    'bg-red-100 text-red-800': !isPresentAtCHU(agent)
                  }"
                >
                  {{ isPresentAtCHU(agent) ? 'Présent au CHU' : 'Parti du CHU' }}
                </span>
              </div>

              <!-- Détails du profil -->
              <dl class="mt-6 flex flex-col sm:ml-0 sm:mt-1 sm:flex-row sm:flex-wrap">
                <dt class="sr-only">Catégorie</dt>
                <dd class="flex items-center text-sm text-gray-500 font-medium capitalize sm:mr-6">
                  <i class="pi pi-briefcase flex-shrink-0 mr-1.5 h-4 w-4 text-gray-400"></i>
                  {{ agent.categorie || 'Non spécifiée' }}
                </dd>

                <dt class="sr-only">Matricule</dt>
                <dd class="mt-3 flex items-center text-sm text-gray-500 font-medium sm:mr-6 sm:mt-0">
                  <i class="pi pi-id-card flex-shrink-0 mr-1.5 h-4 w-4 text-gray-400"></i>
                  {{ agent.matricule || 'Non spécifié' }}
                </dd>

                <dt class="sr-only">ID RH</dt>
                <dd class="mt-3 flex items-center text-sm text-gray-500 font-medium sm:mr-6 sm:mt-0">
                  <i class="pi pi-user flex-shrink-0 mr-1.5 h-4 w-4 text-gray-400"></i>
                  {{ agent.hrUser || 'Non spécifié' }}
                </dd>

                <dt class="sr-only">Email</dt>
                <dd class="mt-3 flex items-center text-sm text-gray-500 font-medium sm:mr-6 sm:mt-0" *ngIf="agent.email">
                  <i class="pi pi-envelope flex-shrink-0 mr-1.5 h-4 w-4 text-gray-400"></i>
                  <a [href]="'mailto:' + agent.email" class="text-cyan-600 hover:text-cyan-800">
                    {{ agent.email }}
                  </a>
                </dd>
              </dl>
            </div>
          </div>
        </div>

        <!-- Actions -->
        <div class="mt-6 flex space-x-3 md:mt-0 md:ml-4">
          <button
            pButton
            label="Retour à la liste"
            icon="pi pi-arrow-left"
            (click)="goBack()"
            class="p-button-outlined p-button-secondary"
          ></button>
        </div>
      </div>
    </div>
  </div>

  <!-- Contenu principal -->
  <div class="px-4 py-6 sm:px-6 lg:px-8">

    <!-- Section Affectations -->
    <div class="bg-white shadow rounded-lg mb-6">
      <div class="px-4 py-5 sm:p-6">
        <div class="flex items-center justify-between mb-4">
          <div class="flex items-center">
            <i class="pi pi-sitemap text-cyan-600 text-xl mr-3"></i>
            <h2 class="text-lg font-medium text-gray-900">Affectations du praticien</h2>
          </div>
          <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            {{ totalAffectations }} affectation(s)
          </span>
        </div>

        <!-- Loading state for affectations -->
        <div *ngIf="affectationsLoading" class="flex justify-center items-center h-32">
          <p-progressSpinner strokeWidth="3" animationDuration="1s"></p-progressSpinner>
        </div>

        <!-- Error state for affectations -->
        <div *ngIf="affectationsError && !affectationsLoading" class="p-4">
          <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
            <strong>Erreur:</strong> {{ affectationsError }}
          </div>
        </div>

        <!-- Affectations table -->
        <div>
          <p-table
            [value]="affectations"
            [paginator]="true"
            [rows]="itemsPerPage"
            [totalRecords]="totalAffectations"
            [lazy]="true"
            (onLazyLoad)="onAffectationsPageChange($event)"
            [loading]="affectationsLoading"
            styleClass="p-datatable-sm p-datatable-striped"
            [scrollable]="true"
            scrollHeight="400px"
          >
            <!-- Table Header -->
            <ng-template pTemplate="header">
              <tr>
                <th class="px-4 py-2 border-b text-left text-gray-700 font-semibold">UF</th>
                <th class="px-4 py-2 border-b text-left text-gray-700 font-semibold">RGT</th>
                <th class="px-4 py-2 border-b text-left text-gray-700 font-semibold">Grade</th>
                <th class="px-4 py-2 border-b text-left text-gray-700 font-semibold">ETP</th>
                <th class="px-4 py-2 border-b text-left text-gray-700 font-semibold">Taux Affectation</th>
                <th class="px-4 py-2 border-b text-left text-gray-700 font-semibold">Détails</th>
                <th class="px-4 py-2 border-b text-left text-gray-700 font-semibold">Entrée</th>
                <th class="px-4 py-2 border-b text-left text-gray-700 font-semibold">Sortie</th>
              </tr>
            </ng-template>

            <!-- Table Body -->
            <ng-template pTemplate="body" let-affectation>
              <tr class="border-b hover:bg-gray-50">
                <!-- UF -->
                <td class="px-4 py-2">
                  <div>
                    <div class="font-medium text-gray-900">
                      {{ getUfDisplay(affectation) }}
                    </div>
                    <div class="mt-1">
                      <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        Effectif: {{ affectation.effectifDeUf || '0' }}
                      </span>
                    </div>
                  </div>
                </td>

                <!-- RGT -->
                <td class="px-4 py-2">
                  <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-sm font-medium">
                    {{ affectation.rgt || 'N/A' }}
                  </span>
                </td>

                <!-- Grade -->
                <td class="px-4 py-2">
                  <span class="px-2 py-1 bg-purple-100 text-purple-800 rounded text-sm font-medium">
                    {{ getGradeDisplay(affectation) }}
                  </span>
                </td>

                <!-- ETP -->
                <td class="px-4 py-2">
                  <span class="font-medium">{{ affectation.etp || '0.00' }}</span>
                </td>

                <!-- Taux Affectation -->
                <td class="px-4 py-2">
                  <span class="font-medium">{{ affectation.tauxAffectation || '0' }}%</span>
                </td>

                <!-- Détails -->
                <td class="px-4 py-2">
                  <div class="space-y-1">
                    <!-- Affectation Principale -->
                    <div class="flex items-center">
                      <i class="pi pi-star text-xs mr-1 text-gray-400"></i>
                      <span
                        class="px-2 py-1 rounded text-xs font-medium cursor-help"
                        [ngClass]="{
                          'bg-green-100 text-green-800': affectation.affectationPrincipale === 'O',
                          'bg-gray-100 text-gray-800': affectation.affectationPrincipale !== 'O'
                        }"
                        [title]="affectation.affectationPrincipale === 'O' ? 'Affectation principale du praticien' : 'Affectation secondaire du praticien'"
                      >
                        {{ affectation.affectationPrincipale === 'O' ? 'Principale' : 'Secondaire' }}
                      </span>
                    </div>

                    <!-- Absences -->
                    <div class="flex items-center">
                      <i class="pi pi-calendar-times text-xs mr-1 text-gray-400"></i>
                      <span class="text-xs text-gray-600">
                        {{ getAbsencesDisplay(affectation) }}
                      </span>
                    </div>
                  </div>
                </td>

                <!-- Entrée (dateDebut) -->
                <td class="px-4 py-2">
                  <span class="text-sm">{{ formatDate(affectation.dateDebut) }}</span>
                </td>

                <!-- Sortie (dateFin) -->
                <td class="px-4 py-2">
                  <span class="text-sm">{{ formatDate(affectation.dateFin) }}</span>
                </td>
              </tr>
            </ng-template>

            <!-- Empty state -->
            <ng-template pTemplate="emptymessage">
              <tr>
                <td colspan="8" class="text-center py-8">
                  <div class="flex flex-col items-center">
                    <i class="pi pi-sitemap text-4xl text-gray-400 mb-4"></i>
                    <p class="text-gray-500 text-lg">Aucune affectation trouvée</p>
                    <p class="text-gray-400 text-sm">Ce praticien n'a pas d'affectations actives</p>
                  </div>
                </td>
              </tr>
            </ng-template>
          </p-table>
        </div>
      </div>
    </div>

    <!-- Section Analyse comparative des actes -->
    <div class="bg-white shadow rounded-lg mb-6">
      <div class="px-4 py-5 sm:p-6">
        <div class="flex items-center justify-between mb-4">
          <div class="flex items-center">
            <i class="pi pi-chart-line text-green-600 text-xl mr-3"></i>
            <h2 class="text-lg font-medium text-gray-900">Analyse comparative des actes</h2>
            <span class="ml-2 text-sm text-gray-500" *ngIf="globalFilters">
              [{{ getPeriodLabel('p1') }} - {{ getPeriodLabel('p2') }} - {{ getPeriodLabel('p3') }}]
            </span>
          </div>
          <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            Actif
          </span>
        </div>

        <!-- Navigation par onglets -->
        <div class="px-4 sm:px-6 lg:max-w-6xl lg:mx-auto lg:px-8 mb-6">
          <ol class="flex items-center w-full text-sm text-gray-500 font-medium sm:text-base">
            <!-- Tab CCAM -->
            <li class="flex md:w-full items-center text-indigo-600 sm:after:content-[''] after:w-full after:h-1 after:border-b after:border-gray-200 after:border-1 after:hidden sm:after:inline-block after:mx-4 xl:after:mx-8">
              <div class="flex items-center whitespace-nowrap after:content-['/'] sm:after:hidden after:mx-2 cursor-pointer" (click)="showActeTable('CCAM')">
                <span
                  [class.bg-indigo-600]="activeActeTable === 'CCAM'"
                  [class.text-white]="activeActeTable === 'CCAM'"
                  [class.bg-gray-100]="activeActeTable !== 'CCAM'"
                  [class.text-gray-600]="activeActeTable !== 'CCAM'"
                  class="w-6 h-6 border border-indigo-200 rounded-full flex justify-center items-center mr-3 text-sm lg:w-10 lg:h-10">
                  1
                </span>
                CCAM
              </div>
            </li>

            <!-- Tab NGAP -->
            <li class="flex md:w-full items-center text-indigo-600 sm:after:content-[''] after:w-full after:h-1 after:border-b after:border-gray-200 after:border-1 after:hidden sm:after:inline-block after:mx-4 xl:after:mx-8">
              <div class="flex items-center whitespace-nowrap after:content-['/'] sm:after:hidden after:mx-2 cursor-pointer" (click)="showActeTable('NGAP')">
                <span
                  [class.bg-indigo-600]="activeActeTable === 'NGAP'"
                  [class.text-white]="activeActeTable === 'NGAP'"
                  [class.bg-gray-100]="activeActeTable !== 'NGAP'"
                  [class.text-gray-600]="activeActeTable !== 'NGAP'"
                  class="w-6 h-6 border border-gray-200 rounded-full flex justify-center items-center mr-3 lg:w-10 lg:h-10">
                  2
                </span>
                NGAP
              </div>
            </li>

            <!-- Tab LABO -->
            <li class="flex md:w-full items-center text-indigo-600">
              <div class="flex items-center cursor-pointer" (click)="showActeTable('LABO')">
                <span
                  [class.bg-indigo-600]="activeActeTable === 'LABO'"
                  [class.text-white]="activeActeTable === 'LABO'"
                  [class.bg-gray-100]="activeActeTable !== 'LABO'"
                  [class.text-gray-600]="activeActeTable !== 'LABO'"
                  class="w-6 h-6 border border-gray-200 rounded-full flex justify-center items-center mr-3 lg:w-10 lg:h-10">
                  3
                </span>
                LABO
              </div>
            </li>
          </ol>
        </div>

        <!-- Contenu des onglets -->
        <div *ngIf="activeActeTable === 'CCAM'" class="p-4">

          <!-- Barre de recherche -->
          <div class="relative mt-2 mb-4 rounded-md shadow-sm">
            <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
              <span class="text-gray-500 sm:text-sm mr-3">
                <i class="pi pi-search"></i>
              </span>
            </div>
            <input
              type="text"
              (input)="ccamTableRef.filterGlobal(onInput($event), 'contains')"
              class="block w-full h-full pl-8 pr-5 py-2 rounded-md border-0 pr-25 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm/6"
              placeholder="Filtrer par code ou description CCAM"
            />
          </div>

          <!-- Tableau CCAM -->
          <p-table
            #ccamTableRef
            [value]="ccamData"
            [scrollable]="true"
            scrollHeight="600px"
            [tableStyle]="{'min-width': '50rem'}"
            [globalFilterFields]="['code', 'description']"
            [loading]="acteDataLoading"
            [virtualScroll]="false"
            [lazy]="false"
          >
            <ng-template pTemplate="header">
              <tr class="border-b hover:bg-gray-50">
                <th class="px-4 py-2 border-b text-left text-gray-700 font-semibold">
                  <i class="pi pi-hashtag cursor-pointer ml-2" title="Voir les détails de l'acte"></i>
                </th>
                <th pSortableColumn="code">Code <p-sortIcon field="code"></p-sortIcon></th>
                <th pSortableColumn="description">Description <p-sortIcon field="description"></p-sortIcon></th>
                <th pSortableColumn="totalP1" class="text-center">
                  <div class="flex flex-col items-center gap-1">
                    <div class="period-header period-current">
                      <span class="period-label">{{ getPeriodLabel('p1') }}</span>
                      <div class="period-indicator current"></div>
                    </div>
                    <p-sortIcon field="totalP1"></p-sortIcon>
                  </div>
                </th>
                <th pSortableColumn="totalP2" class="text-center">
                  <div class="flex flex-col items-center gap-1">
                    <div class="period-header period-previous">
                      <span class="period-label">{{ getPeriodLabel('p2') }}</span>
                      <div class="period-indicator previous"></div>
                    </div>
                    <p-sortIcon field="totalP2"></p-sortIcon>
                  </div>
                </th>
                <th pSortableColumn="totalP3" class="text-center">
                  <div class="flex flex-col items-center gap-1">
                    <div class="period-header period-historical">
                      <span class="period-label">{{ getPeriodLabel('p3') }}</span>
                      <div class="period-indicator historical"></div>
                    </div>
                    <p-sortIcon field="totalP3"></p-sortIcon>
                  </div>
                </th>
              </tr>
            </ng-template>

            <ng-template pTemplate="body" let-acte>
              <tr class="border-b hover:bg-gray-50">
                <td class="px-4 py-2">
                  <i class="pi pi-eye cursor-pointer text-blue-600 hover:text-blue-800"
                     (click)="viewActeDetails(acte)"
                     title="Voir les détails"></i>
                </td>
                <td>{{ acte.code }}</td>
                <td>{{ acte.description }}</td>
                <td class="text-center font-semibold text-blue-600">{{ acte.totalP1 || 0 }}</td>
                <td class="text-center font-semibold text-orange-600">{{ acte.totalP2 || 0 }}</td>
                <td class="text-center font-semibold text-purple-600">{{ acte.totalP3 || 0 }}</td>
              </tr>
            </ng-template>

            <!-- Ligne de total et interventions -->
            <ng-template pTemplate="footer">
              <!-- Ligne des totaux d'actes -->
              <tr class="bg-gray-50">
                <td colspan="3" class="font-bold text-left py-3">
                  <i class="pi pi-chart-bar text-gray-600 mr-2"></i>
                  TOTAL ACTES
                </td>
                <td class="text-center">
                  <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-bold bg-blue-100 text-blue-800">
                    {{ getTotalForPeriodByTypeActe('p1', 'CCAM') }}
                  </span>
                </td>
                <td class="text-center">
                  <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-bold bg-orange-100 text-orange-800">
                    {{ getTotalForPeriodByTypeActe('p2', 'CCAM') }}
                  </span>
                </td>
                <td class="text-center">
                  <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-bold bg-purple-100 text-purple-800">
                    {{ getTotalForPeriodByTypeActe('p3', 'CCAM') }}
                  </span>
                </td>
              </tr>

              <!-- Ligne des interventions -->
              <tr class="bg-blue-50 border-t-2 border-blue-200">
                <td colspan="3" class="font-bold text-left py-3">
                  <i class="pi pi-hospital text-blue-600 mr-2"></i>
                  <span class="text-blue-800">INTERVENTIONS</span>
                </td>
                <td class="text-center">
                  <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-bold bg-blue-200 text-blue-900">
                    {{ ccamInterventionStats.interventionsP1 }}
                  </span>
                </td>
                <td class="text-center">
                  <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-bold bg-orange-200 text-orange-900">
                    {{ ccamInterventionStats.interventionsP2 }}
                  </span>
                </td>
                <td class="text-center">
                  <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-bold bg-purple-200 text-purple-900">
                    {{ ccamInterventionStats.interventionsP3 }}
                  </span>
                </td>
              </tr>
            </ng-template>
          </p-table>
        </div>

        <!-- Contenu onglet NGAP -->
        <div *ngIf="activeActeTable === 'NGAP'" class="p-4">

          <!-- Barre de recherche -->
          <div class="relative mt-2 mb-4 rounded-md shadow-sm">
            <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
              <span class="text-gray-500 sm:text-sm mr-3">
                <i class="pi pi-search"></i>
              </span>
            </div>
            <input
              type="text"
              (input)="ngapTableRef.filterGlobal(onInput($event), 'contains')"
              class="block w-full h-full pl-8 pr-5 py-2 rounded-md border-0 pr-25 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm/6"
              placeholder="Filtrer par code ou description NGAP"
            />
          </div>

          <!-- Tableau NGAP -->
          <p-table
            #ngapTableRef
            [value]="ngapData"
            [scrollable]="true"
            scrollHeight="600px"
            [tableStyle]="{'min-width': '50rem'}"
            [globalFilterFields]="['code', 'description']"
            [loading]="acteDataLoading"
            [virtualScroll]="false"
            [lazy]="false"
          >
            <ng-template pTemplate="header">
              <tr class="border-b hover:bg-gray-50">
                <th class="px-4 py-2 border-b text-left text-gray-700 font-semibold">
                  <i class="pi pi-hashtag cursor-pointer ml-2" title="Voir les détails de l'acte"></i>
                </th>
                <th pSortableColumn="code">Code <p-sortIcon field="code"></p-sortIcon></th>
                <th pSortableColumn="description">Description <p-sortIcon field="description"></p-sortIcon></th>
                <th pSortableColumn="totalP1" class="text-center">
                  <div class="flex flex-col items-center gap-1">
                    <div class="period-header period-current">
                      <span class="period-label">{{ getPeriodLabel('p1') }}</span>
                      <div class="period-indicator current"></div>
                    </div>
                    <p-sortIcon field="totalP1"></p-sortIcon>
                  </div>
                </th>
                <th pSortableColumn="totalP2" class="text-center">
                  <div class="flex flex-col items-center gap-1">
                    <div class="period-header period-previous">
                      <span class="period-label">{{ getPeriodLabel('p2') }}</span>
                      <div class="period-indicator previous"></div>
                    </div>
                    <p-sortIcon field="totalP2"></p-sortIcon>
                  </div>
                </th>
                <th pSortableColumn="totalP3" class="text-center">
                  <div class="flex flex-col items-center gap-1">
                    <div class="period-header period-historical">
                      <span class="period-label">{{ getPeriodLabel('p3') }}</span>
                      <div class="period-indicator historical"></div>
                    </div>
                    <p-sortIcon field="totalP3"></p-sortIcon>
                  </div>
                </th>
              </tr>
            </ng-template>

            <ng-template pTemplate="body" let-acte>
              <tr class="border-b hover:bg-gray-50">
                <td class="px-4 py-2">
                  <i class="pi pi-eye cursor-pointer text-blue-600 hover:text-blue-800"
                     (click)="viewActeDetails(acte)"
                     title="Voir les détails"></i>
                </td>
                <td>{{ acte.code }}</td>
                <td>{{ acte.description }}</td>
                <td class="text-center font-semibold text-blue-600">{{ acte.totalP1 || 0 }}</td>
                <td class="text-center font-semibold text-orange-600">{{ acte.totalP2 || 0 }}</td>
                <td class="text-center font-semibold text-purple-600">{{ acte.totalP3 || 0 }}</td>
              </tr>
            </ng-template>

            <!-- Ligne de total et interventions -->
            <ng-template pTemplate="footer">
              <!-- Ligne des totaux d'actes -->
              <tr class="bg-gray-50">
                <td colspan="3" class="font-bold text-left py-3">
                  <i class="pi pi-chart-bar text-gray-600 mr-2"></i>
                  TOTAL ACTES
                </td>
                <td class="text-center">
                  <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-bold bg-blue-100 text-blue-800">
                    {{ getTotalForPeriodByTypeActe('p1', 'NGAP') }}
                  </span>
                </td>
                <td class="text-center">
                  <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-bold bg-orange-100 text-orange-800">
                    {{ getTotalForPeriodByTypeActe('p2', 'NGAP') }}
                  </span>
                </td>
                <td class="text-center">
                  <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-bold bg-purple-100 text-purple-800">
                    {{ getTotalForPeriodByTypeActe('p3', 'NGAP') }}
                  </span>
                </td>
              </tr>

              <!-- Ligne des interventions -->
              <tr class="bg-blue-50 border-t-2 border-blue-200">
                <td colspan="3" class="font-bold text-left py-3">
                  <i class="pi pi-hospital text-blue-600 mr-2"></i>
                  <span class="text-blue-800">INTERVENTIONS</span>
                </td>
                <td class="text-center">
                  <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-bold bg-blue-200 text-blue-900">
                    {{ ngapInterventionStats.interventionsP1 }}
                  </span>
                </td>
                <td class="text-center">
                  <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-bold bg-orange-200 text-orange-900">
                    {{ ngapInterventionStats.interventionsP2 }}
                  </span>
                </td>
                <td class="text-center">
                  <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-bold bg-purple-200 text-purple-900">
                    {{ ngapInterventionStats.interventionsP3 }}
                  </span>
                </td>
              </tr>
            </ng-template>
          </p-table>
        </div>

        <!-- Contenu onglet LABO -->
        <div *ngIf="activeActeTable === 'LABO'" class="p-4">
          <!-- Barre de recherche -->
          <div class="relative mt-2 mb-4 rounded-md shadow-sm">
            <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
              <span class="text-gray-500 sm:text-sm mr-3">
                <i class="pi pi-search"></i>
              </span>
            </div>
            <input
              type="text"
              (input)="laboTableRef.filterGlobal(onInput($event), 'contains')"
              class="block w-full h-full pl-8 pr-5 py-2 rounded-md border-0 pr-25 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm/6"
              placeholder="Filtrer par code ou description LABO"
            />
          </div>

          <!-- Tableau LABO -->
          <p-table
            #laboTableRef
            [value]="laboData"
            [scrollable]="true"
            scrollHeight="600px"
            [tableStyle]="{'min-width': '50rem'}"
            [globalFilterFields]="['code', 'description']"
            [loading]="acteDataLoading"
            [virtualScroll]="false"
            [lazy]="false"
          >
            <ng-template pTemplate="header">
              <tr class="border-b hover:bg-gray-50">
                <th class="px-4 py-2 border-b text-left text-gray-700 font-semibold">
                  <i class="pi pi-hashtag cursor-pointer ml-2" title="Voir les détails de l'acte"></i>
                </th>
                <th pSortableColumn="code">Code <p-sortIcon field="code"></p-sortIcon></th>
                <th pSortableColumn="description">Description <p-sortIcon field="description"></p-sortIcon></th>
                <th pSortableColumn="totalP1" class="text-center">
                  <div class="flex flex-col items-center gap-1">
                    <div class="period-header period-current">
                      <span class="period-label">{{ getPeriodLabel('p1') }}</span>
                      <div class="period-indicator current"></div>
                    </div>
                    <p-sortIcon field="totalP1"></p-sortIcon>
                  </div>
                </th>
                <th pSortableColumn="totalP2" class="text-center">
                  <div class="flex flex-col items-center gap-1">
                    <div class="period-header period-previous">
                      <span class="period-label">{{ getPeriodLabel('p2') }}</span>
                      <div class="period-indicator previous"></div>
                    </div>
                    <p-sortIcon field="totalP2"></p-sortIcon>
                  </div>
                </th>
                <th pSortableColumn="totalP3" class="text-center">
                  <div class="flex flex-col items-center gap-1">
                    <div class="period-header period-historical">
                      <span class="period-label">{{ getPeriodLabel('p3') }}</span>
                      <div class="period-indicator historical"></div>
                    </div>
                    <p-sortIcon field="totalP3"></p-sortIcon>
                  </div>
                </th>
              </tr>
            </ng-template>

            <ng-template pTemplate="body" let-acte>
              <tr class="border-b hover:bg-gray-50">
                <td class="px-4 py-2">
                  <i class="pi pi-eye cursor-pointer text-blue-600 hover:text-blue-800"
                     (click)="viewActeDetails(acte)"
                     title="Voir les détails"></i>
                </td>
                <td>{{ acte.code }}</td>
                <td>{{ acte.description }}</td>
                <td class="text-center font-semibold text-blue-600">{{ acte.totalP1 || 0 }}</td>
                <td class="text-center font-semibold text-orange-600">{{ acte.totalP2 || 0 }}</td>
                <td class="text-center font-semibold text-purple-600">{{ acte.totalP3 || 0 }}</td>
              </tr>
            </ng-template>

            <!-- Ligne de total et interventions -->
            <ng-template pTemplate="footer">
              <!-- Ligne des totaux d'actes -->
              <tr class="bg-gray-50">
                <td colspan="3" class="font-bold text-left py-3">
                  <i class="pi pi-chart-bar text-gray-600 mr-2"></i>
                  TOTAL ACTES
                </td>
                <td class="text-center">
                  <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-bold bg-blue-100 text-blue-800">
                    {{ getTotalForPeriodByTypeActe('p1', 'LABO') }}
                  </span>
                </td>
                <td class="text-center">
                  <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-bold bg-orange-100 text-orange-800">
                    {{ getTotalForPeriodByTypeActe('p2', 'LABO') }}
                  </span>
                </td>
                <td class="text-center">
                  <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-bold bg-purple-100 text-purple-800">
                    {{ getTotalForPeriodByTypeActe('p3', 'LABO') }}
                  </span>
                </td>
              </tr>

              <!-- Ligne des interventions -->
              <tr class="bg-blue-50 border-t-2 border-blue-200">
                <td colspan="3" class="font-bold text-left py-3">
                  <i class="pi pi-hospital text-blue-600 mr-2"></i>
                  <span class="text-blue-800">INTERVENTIONS</span>
                </td>
                <td class="text-center">
                  <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-bold bg-blue-200 text-blue-900">
                    {{ laboInterventionStats.interventionsP1 }}
                  </span>
                </td>
                <td class="text-center">
                  <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-bold bg-orange-200 text-orange-900">
                    {{ laboInterventionStats.interventionsP2 }}
                  </span>
                </td>
                <td class="text-center">
                  <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-bold bg-purple-200 text-purple-900">
                    {{ laboInterventionStats.interventionsP3 }}
                  </span>
                </td>
              </tr>
            </ng-template>
          </p-table>
        </div>
      </div>
    </div>

    <!-- Section Graphiques & Statistiques -->
    <div class="bg-white shadow rounded-lg">
      <div class="px-4 py-5 sm:p-6">
        <div class="flex items-center justify-between mb-4">
          <div class="flex items-center">
            <i class="pi pi-chart-pie text-orange-600 text-xl mr-3"></i>
            <h2 class="text-lg font-medium text-gray-900">Graphiques et statistiques</h2>
          </div>
        </div>

        <app-single-praticien-charts
          [ccamData]="ccamData"
          [ngapData]="ngapData"
          [laboData]="laboData"
          [p1]="globalFilters?.p1 || null"
          [p2]="globalFilters?.p2 || null"
          [p3]="globalFilters?.p3 || null">
        </app-single-praticien-charts>
      </div>
    </div>

  </div>
</div>
