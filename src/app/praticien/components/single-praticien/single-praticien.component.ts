import { Compo<PERSON>, <PERSON><PERSON>ni<PERSON>, On<PERSON><PERSON>roy } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Subscription } from 'rxjs';

// Models
import { AgentModel } from '../../../core/models/praticien/AgentModel';
import { AffectationModel } from '../../../core/models/praticien/AffectationModel';

// Services
import { AgentService } from '../../../core/services/praticien/agent.service';
import { AffectationService } from '../../../core/services/praticien/affectation.service';
import { GlobalFilterService, GlobalFilterState, DatePeriod } from '../../../core/services/global-filter';
import { PraticienActesService, PraticienActeData, InterventionStats } from '../../../core/services/praticien/praticien-actes.service';

// Breadcrumb
export interface BreadcrumbItem {
  label: string;
  url: string;
}

@Component({
  selector: 'app-single-praticien',
  templateUrl: './single-praticien.component.html',
  styleUrls: ['./single-praticien.component.scss'],
  styles: [`
    /* Styles modernes pour les en-têtes de périodes */
    .period-header {
      position: relative;
      display: inline-flex;
      flex-direction: column;
      align-items: center;
      padding: 8px 16px;
      border-radius: 12px;
      font-weight: 600;
      font-size: 0.875rem;
      transition: all 0.2s ease;
      min-width: 120px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .period-header:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .period-label {
      font-weight: 600;
      letter-spacing: 0.025em;
    }

    .period-indicator {
      width: 100%;
      height: 3px;
      border-radius: 2px;
      margin-top: 4px;
      transition: all 0.2s ease;
    }

    /* Période actuelle (P1) - Bleu moderne */
    .period-current {
      background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
      color: white;
    }

    .period-current .period-indicator.current {
      background: rgba(255, 255, 255, 0.3);
    }

    .period-current:hover {
      background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
    }

    /* Période précédente (P2) - Orange moderne */
    .period-previous {
      background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
      color: white;
    }

    .period-previous .period-indicator.previous {
      background: rgba(255, 255, 255, 0.3);
    }

    .period-previous:hover {
      background: linear-gradient(135deg, #e5a00b 0%, #c2710c 100%);
    }

    /* Période historique (P3) - Violet moderne */
    .period-historical {
      background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
      color: white;
    }

    .period-historical .period-indicator.historical {
      background: rgba(255, 255, 255, 0.3);
    }

    .period-historical:hover {
      background: linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%);
    }

    /* Animation pour les indicateurs */
    .period-header:hover .period-indicator {
      height: 4px;
      background: rgba(255, 255, 255, 0.5) !important;
    }

    /* Responsive design */
    @media (max-width: 768px) {
      .period-header {
        min-width: 100px;
        padding: 6px 12px;
        font-size: 0.75rem;
      }
    }
  `]
})
export class SinglePraticienComponent implements OnInit, OnDestroy {
  // Breadcrumb configuration
  breadcrumbItems: BreadcrumbItem[] = [
    { label: 'Accueil', url: '/' },
    { label: 'Praticiens', url: '/praticien/praticien-list' },
    { label: 'Détails du Praticien', url: '' }
  ];

  // Data properties
  praticienId: string = '';
  agent: AgentModel | null = null;
  affectations: AffectationModel[] = [];

  // Loading and error states
  loading: boolean = true;
  affectationsLoading: boolean = true;
  error: string = '';
  affectationsError: string = '';

  // Pagination for affectations
  currentPage: number = 1;
  itemsPerPage: number = 10;
  totalAffectations: number = 0;

  // Flag to prevent multiple simultaneous calls
  private isLoadingAffectations: boolean = false;

  // Global filters and periods
  globalFilters: GlobalFilterState | null = null;

  // Analyse comparative des actes - Data properties
  ccamData: PraticienActeData[] = [];
  ngapData: PraticienActeData[] = [];
  laboData: PraticienActeData[] = [];
  acteDataLoading: boolean = false;

  // Analyse comparative des actes - Intervention stats
  ccamInterventionStats: InterventionStats = { interventionsP1: 0, interventionsP2: 0, interventionsP3: 0 };
  ngapInterventionStats: InterventionStats = { interventionsP1: 0, interventionsP2: 0, interventionsP3: 0 };
  laboInterventionStats: InterventionStats = { interventionsP1: 0, interventionsP2: 0, interventionsP3: 0 };

  // Analyse comparative des actes - UI state
  activeActeTable: 'CCAM' | 'NGAP' | 'LABO' = 'CCAM';

  // Subscriptions
  private subscriptions: Subscription[] = [];

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private agentService: AgentService,
    private affectationService: AffectationService,
    private globalFilterService: GlobalFilterService,
    private praticienActesService: PraticienActesService
  ) {}

  ngOnInit(): void {
    // Get praticien ID from route
    this.praticienId = this.route.snapshot.paramMap.get('id') || '';

    if (!this.praticienId) {
      this.error = 'ID du praticien manquant';
      this.loading = false;
      return;
    }

    this.loadPraticienDetails();

    // Load global filters
    this.loadGlobalFilters();

    // Subscribe to global filter changes (for periods)
    const filterSubscription = this.globalFilterService.getFilterState().subscribe(filters => {
      console.log('🔄 Global filters changed:', filters);
      this.globalFilters = filters;

      // Reload affectations if agent is already loaded and we have hrUser
      if (this.agent && this.agent.hrUser) {
        console.log('🔄 Reloading affectations with new periods');
        this.loadAffectations();

        // Also reload actes data with new periods
        console.log('🔄 Reloading actes data with new periods');
        this.loadActesData();
      }
    });

    this.subscriptions.push(filterSubscription);

    // Note: loadAffectations() sera appelé après le chargement de l'agent (pour avoir hrUser)
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  /**
   * Load praticien details
   */
  private loadPraticienDetails(): void {
    this.loading = true;
    this.error = '';

    const subscription = this.agentService.getById(this.praticienId).subscribe({
      next: (agent) => {
        this.agent = agent;
        this.loading = false;

        // Update breadcrumb with agent name
        this.breadcrumbItems[2].label = this.getFullNameWithTitle(agent);

        console.log('✅ Agent loaded:', agent);

        // Now that we have the agent data with hrUser, we can load affectations
        // But only if p-table hasn't already triggered the load
        if (agent.hrUser && this.affectations.length === 0 && !this.isLoadingAffectations) {
          console.log('🔄 Triggering initial affectations load with hrUser:', agent.hrUser);
          this.loadAffectations();
        }

        // Load actes data if we have hrUser
        if (agent.hrUser) {
          console.log('🔄 Triggering initial actes data load with hrUser:', agent.hrUser);
          this.loadActesData();
        }
      },
      error: (error) => {
        console.error('❌ Error loading agent:', error);
        this.error = 'Erreur lors du chargement des détails du praticien';
        this.loading = false;
      }
    });

    this.subscriptions.push(subscription);
  }

  /**
   * Load global filters (periods)
   */
  private loadGlobalFilters(): void {
    this.globalFilters = this.globalFilterService.getCurrentFilterState();
    console.log('🌍 Loaded global filters:', this.globalFilters);
  }

  /**
   * Load affectations for this agent using hrUser
   */
  private loadAffectations(): void {
    // Prevent multiple simultaneous calls
    if (this.isLoadingAffectations) {
      console.log('🔄 Already loading affectations, skipping...');
      return;
    }

    // Check if we have the agent data and hrUser
    if (!this.agent || !this.agent.hrUser) {
      console.log('⚠️ Cannot load affectations: agent or hrUser not available');
      this.affectationsError = 'Impossible de charger les affectations: identifiant RH manquant';
      return;
    }

    this.isLoadingAffectations = true;
    this.affectationsLoading = true;
    this.affectationsError = '';

    console.log(`🔍 Loading affectations for agent.hrUser: ${this.agent.hrUser}, page ${this.currentPage}`);

    // Prepare filters with periods
    const filters = this.buildFiltersWithPeriods();

    const subscription = this.affectationService.getAffectationsByAgentHrUserWithFilters(
      this.agent.hrUser,
      this.currentPage,
      this.itemsPerPage,
      filters
    ).subscribe({
      next: (result) => {
        this.affectations = result.items;
        this.totalAffectations = result.totalItems;
        this.affectationsLoading = false;
        this.isLoadingAffectations = false;

        console.log(`✅ Loaded ${this.affectations.length} affectations (total: ${this.totalAffectations})`);
        console.log('📊 First affectation data:', this.affectations[0]);

        // Log UFS data specifically
        if (this.affectations.length > 0 && this.affectations[0].ufs) {
          console.log('🏥 First UFS data:', this.affectations[0].ufs);
        }
      },
      error: (error) => {
        console.error('❌ Error loading affectations:', error);
        this.affectationsError = 'Erreur lors du chargement des affectations';
        this.affectationsLoading = false;
        this.isLoadingAffectations = false;
      }
    });

    this.subscriptions.push(subscription);
  }

  /**
   * Handle lazy loading for affectations (called by p-table)
   */
  onAffectationsPageChange(event: any): void {
    console.log('📄 Lazy load event:', event);

    // Prevent multiple simultaneous calls
    if (this.isLoadingAffectations) {
      console.log('🔄 Already loading affectations, skipping lazy load...');
      return;
    }

    // Update pagination info
    this.currentPage = (event.first / event.rows) + 1;
    this.itemsPerPage = event.rows;

    console.log(`📄 Loading page ${this.currentPage} with ${this.itemsPerPage} items`);

    // Load affectations
    this.loadAffectations();
  }

  /**
   * Get full name with title for display
   */
  getFullNameWithTitle(agent: AgentModel): string {
    const parts = [];
    if (agent.titre) parts.push(agent.titre);
    if (agent.nom) parts.push(agent.nom);
    if (agent.prenom) parts.push(agent.prenom);

    return parts.length > 0 ? parts.join(' ') : 'Nom inconnu';
  }

  /**
   * Get initials for avatar
   */
  getInitials(agent: AgentModel): string {
    const parts = [];
    if (agent.prenom) parts.push(agent.prenom.charAt(0).toUpperCase());
    if (agent.nom) parts.push(agent.nom.charAt(0).toUpperCase());

    return parts.length > 0 ? parts.join('') : '??';
  }

  /**
   * Get UF display (ufcode + libelle)
   */
  getUfDisplay(affectation: AffectationModel): string {
    console.log('🏥 Affectation UFS data:', affectation.ufs);

    if (!affectation.ufs) {
      return 'UF N/A';
    }

    const ufcode = affectation.ufs.ufcode || '';
    const libelle = affectation.ufs.libelle || '';

    if (ufcode && libelle) {
      return `${ufcode} - ${libelle}`;
    } else if (ufcode) {
      return ufcode;
    } else if (libelle) {
      return libelle;
    } else {
      return 'UF N/A';
    }
  }

  /**
   * Get Grade display (typeGrade + libelleGrade)
   */
  getGradeDisplay(affectation: AffectationModel): string {
    const typeGrade = affectation.typeGrade || '';
    const libelleGrade = affectation.libelleGrade || '';

    if (typeGrade && libelleGrade) {
      return `${typeGrade} - ${libelleGrade}`;
    } else if (typeGrade) {
      return typeGrade;
    } else if (libelleGrade) {
      return libelleGrade;
    } else {
      return 'N/A';
    }
  }

  /**
   * Get Absences display (user-friendly format)
   */
  getAbsencesDisplay(affectation: AffectationModel): string {
    const absences = affectation.absences || 0;

    if (absences === 0) {
      return 'Aucune absence';
    } else if (absences === 1) {
      return '1 absence';
    } else {
      return `${absences} absences`;
    }
  }

  /**
   * Check if agent is present at CHU based on dateVenue and dateDepart
   */
  isPresentAtCHU(agent: AgentModel): boolean {
    const today = new Date();

    // Si pas de date de venue, on considère qu'il n'est pas présent
    if (!agent.dateVenue) {
      return false;
    }

    try {
      const dateVenue = new Date(agent.dateVenue);

      // Si la date de venue est dans le futur, pas encore présent
      if (dateVenue > today) {
        return false;
      }

      // Si pas de date de départ, il est présent
      if (!agent.dateDepart) {
        return true;
      }

      const dateDepart = new Date(agent.dateDepart);

      // Si la date de départ est dans le futur ou aujourd'hui, il est encore présent
      return dateDepart >= today;

    } catch (error) {
      console.error('Error parsing dates for agent presence:', error);
      // En cas d'erreur de parsing, on se base sur isActif comme fallback
      return agent.isActif;
    }
  }

  /**
   * Format date for display
   */
  formatDate(dateString: string | null): string {
    if (!dateString) return 'Pas connu de supra';

    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('fr-FR', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      });
    } catch (error) {
      return 'Date invalide';
    }
  }

  /**
   * Build filters with periods from global state
   */
  private buildFiltersWithPeriods(): any {
    const filters: any = {};

    if (this.globalFilters) {
      // Add P1 period
      if (this.globalFilters.p1 && this.globalFilters.p1.dateDebut && this.globalFilters.p1.dateFin) {
        filters.p1Start = this.formatDateForApi(this.globalFilters.p1.dateDebut);
        filters.p1End = this.formatDateForApi(this.globalFilters.p1.dateFin);
      }

      // Add P2 period
      if (this.globalFilters.p2 && this.globalFilters.p2.dateDebut && this.globalFilters.p2.dateFin) {
        filters.p2Start = this.formatDateForApi(this.globalFilters.p2.dateDebut);
        filters.p2End = this.formatDateForApi(this.globalFilters.p2.dateFin);
      }

      // Add P3 period
      if (this.globalFilters.p3 && this.globalFilters.p3.dateDebut && this.globalFilters.p3.dateFin) {
        filters.p3Start = this.formatDateForApi(this.globalFilters.p3.dateDebut);
        filters.p3End = this.formatDateForApi(this.globalFilters.p3.dateFin);
      }

      console.log('📅 Built filters with periods:', filters);
    }

    return filters;
  }

  /**
   * Format date for API (YYYY-MM-DD format)
   */
  private formatDateForApi(date: Date): string {
    return date.toISOString().split('T')[0];
  }

  /**
   * Go back to praticien list
   */
  goBack(): void {
    this.router.navigate(['/praticien/praticien-list']);
  }

  // ===== ANALYSE COMPARATIVE DES ACTES =====

  /**
   * Load actes data for all types (CCAM, NGAP, LABO)
   */
  private loadActesData(): void {
    if (!this.agent || !this.agent.hrUser) {
      console.warn('⚠️ Cannot load actes data: agent or hrUser not available');
      return;
    }

    console.log('📊 Loading actes data for agent:', this.agent.hrUser);
    this.acteDataLoading = true;

    // Load data for each type
    this.loadActesDataByType('CCAM');
    this.loadActesDataByType('NGAP');
    this.loadActesDataByType('LABO');
  }

  /**
   * Load actes data for a specific type
   */
  private loadActesDataByType(typeActe: 'CCAM' | 'NGAP' | 'LABO'): void {
    if (!this.agent || !this.agent.hrUser) return;

    const periods = this.getPeriods();

    const subscription = this.praticienActesService.getActesByPraticienAndType(
      this.agent.hrUser,
      typeActe,
      periods.p1,
      periods.p2,
      periods.p3
    ).subscribe({
      next: (response) => {
        switch (typeActe) {
          case 'CCAM':
            this.ccamData = [...response.actes]; // Créer une nouvelle référence
            this.ccamInterventionStats = response.interventionStats;
            console.log('📊 CCAM data assigned:', this.ccamData.slice(0, 3));
            console.log('🏥 CCAM interventions:', this.ccamInterventionStats);
            break;
          case 'NGAP':
            this.ngapData = [...response.actes]; // Créer une nouvelle référence
            this.ngapInterventionStats = response.interventionStats;
            console.log('📊 NGAP data assigned:', this.ngapData.slice(0, 3));
            console.log('🏥 NGAP interventions:', this.ngapInterventionStats);
            break;
          case 'LABO':
            this.laboData = [...response.actes]; // Créer une nouvelle référence
            this.laboInterventionStats = response.interventionStats;
            console.log('📊 LABO data assigned:', this.laboData.slice(0, 3));
            console.log('🏥 LABO interventions:', this.laboInterventionStats);
            break;
        }

        console.log(`✅ Loaded ${response.actes.length} ${typeActe} actes`);

        this.acteDataLoading = false;
      },
      error: (error) => {
        console.error(`❌ Error loading ${typeActe} actes:`, error);
        this.acteDataLoading = false;
      }
    });

    this.subscriptions.push(subscription);
  }

  /**
   * Get periods from global filters
   */
  private getPeriods(): { p1: DatePeriod | null, p2: DatePeriod | null, p3: DatePeriod | null } {
    return {
      p1: this.globalFilters?.p1 || null,
      p2: this.globalFilters?.p2 || null,
      p3: this.globalFilters?.p3 || null
    };
  }

  /**
   * Show specific acte table tab
   */
  showActeTable(tableType: 'CCAM' | 'NGAP' | 'LABO'): void {
    this.activeActeTable = tableType;
    console.log('📊 Switched to', tableType, 'table');
  }

  /**
   * Get period label for display
   */
  getPeriodLabel(period: 'p1' | 'p2' | 'p3'): string {
    if (!this.globalFilters || !this.globalFilters[period]) {
      return period.toUpperCase();
    }

    const periodData = this.globalFilters[period];
    if (!periodData || !periodData.dateDebut || !periodData.dateFin) {
      return period.toUpperCase();
    }

    // Format: "Jan 2025 - Jun 2025" ou "2025" si même année
    const startDate = periodData.dateDebut;
    const endDate = periodData.dateFin;

    const startMonth = startDate.toLocaleDateString('fr-FR', { month: 'short' });
    const endMonth = endDate.toLocaleDateString('fr-FR', { month: 'short' });
    const startYear = startDate.getFullYear();
    const endYear = endDate.getFullYear();

    if (startYear === endYear) {
      if (startMonth === endMonth) {
        return `${startMonth} ${startYear}`;
      } else {
        return `${startMonth}-${endMonth} ${startYear}`;
      }
    } else {
      return `${startMonth} ${startYear} - ${endMonth} ${endYear}`;
    }
  }

  /**
   * Get total for a specific period and acte type
   */
  getTotalForPeriodByTypeActe(period: 'p1' | 'p2' | 'p3', typeActe: 'CCAM' | 'NGAP' | 'LABO'): number {
    let data: PraticienActeData[] = [];

    switch (typeActe) {
      case 'CCAM':
        data = this.ccamData;
        break;
      case 'NGAP':
        data = this.ngapData;
        break;
      case 'LABO':
        data = this.laboData;
        break;
      default:
        return 0;
    }

    return data.reduce((sum, item) => {
      switch (period) {
        case 'p1':
          return sum + (item.totalP1 || 0);
        case 'p2':
          return sum + (item.totalP2 || 0);
        case 'p3':
          return sum + (item.totalP3 || 0);
        default:
          return sum;
      }
    }, 0);
  }

  /**
   * Handle input events for table filtering
   */
  onInput(event: Event): string {
    return (event.target as HTMLInputElement).value;
  }

  /**
   * View acte details - Navigate to single-activite
   */
  viewActeDetails(acte: PraticienActeData): void {
    console.log('👁️ Navigating to acte details:', acte);

    // Extraire l'UUID de l'ID Hydra
    // Format: "/api/actes/1f081ed3-eb68-638a-9574-f7f09a7eb30c"
    const uuid = acte.id.split('/').pop();

    if (uuid) {
      // Naviguer vers le module activites avec l'UUID
      this.router.navigate(['/activites/activite', uuid]);
    } else {
      console.error('❌ Unable to extract UUID from acte ID:', acte.id);
    }
  }



}
