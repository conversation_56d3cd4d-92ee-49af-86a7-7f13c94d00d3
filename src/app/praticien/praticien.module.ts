import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

// PrimeNG imports
import { TableModule } from 'primeng/table';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { DropdownModule } from 'primeng/dropdown';
import { CalendarModule } from 'primeng/calendar';
import { CheckboxModule } from 'primeng/checkbox';
import { TooltipModule } from 'primeng/tooltip';
import { CardModule } from 'primeng/card';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { AutoCompleteModule } from 'primeng/autocomplete';
import { ChipsModule } from 'primeng/chips';
import { BadgeModule } from 'primeng/badge';
import { TagModule } from 'primeng/tag';
import { SkeletonModule } from 'primeng/skeleton';
import { PanelModule } from 'primeng/panel';
import { AccordionModule } from 'primeng/accordion';
import { TabViewModule } from 'primeng/tabview';
import { ChartModule } from 'primeng/chart';
import { OverlayPanelModule } from 'primeng/overlaypanel';
import { SplitButtonModule } from 'primeng/splitbutton';
import { MenuModule } from 'primeng/menu';

// Shared components
import { BreadcrumbComponent } from '../pages/breadcrumb/breadcrumb.component';

// Routing
import { PraticienRoutingModule } from './praticien-routing.module';

// Components
import { PraticienListComponent } from './components/praticien-list/praticien-list.component';
import { SinglePraticienComponent } from './components/single-praticien/single-praticien.component';
import { PraticienSubheaderComponent } from './components/praticien-list/subheader/subheader.component';
import { SinglePraticienChartsComponent } from './components/single-praticien/charts/single-praticien-charts.component';

// Services
import { AffectationService } from '../core/services/praticien/affectation.service';

@NgModule({
  declarations: [
    PraticienListComponent,
    SinglePraticienComponent,
    PraticienSubheaderComponent,
    SinglePraticienChartsComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    PraticienRoutingModule,

    // PrimeNG modules
    TableModule,
    ButtonModule,
    InputTextModule,
    DropdownModule,
    CalendarModule,
    CheckboxModule,
    TooltipModule,
    CardModule,
    ProgressSpinnerModule,
    AutoCompleteModule,
    ChipsModule,
    BadgeModule,
    TagModule,
    SkeletonModule,
    PanelModule,
    AccordionModule,
    TabViewModule,
    ChartModule,
    OverlayPanelModule,
    SplitButtonModule,
    MenuModule,

    // Shared components
    BreadcrumbComponent
  ],
  providers: [
    AffectationService
  ]
})
export class PraticienModule { }
